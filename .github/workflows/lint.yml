name: lint

on:
  pull_request:
  merge_group:
    branches:
      - main

env:
  CARGO_TERM_COLOR: always
  CLICOLOR: true
  CI: true
  GITHUB_ACTIONS: true
  APTOS_NETWORK: local
  MOVE_VERSION: 2.1
  COMPILER_VERSION: 2.0
  UPGRADE_CONTRACTS: false
  ARTIFACTS_LEVEL: all
  AAVE_LARGE_PACKAGES_PRIVATE_KEY: ${{ secrets.GH_AAVE_LARGE_PACKAGES_PRIVATE_KEY }}  # temp hack: remove after large packages is no longer needed

jobs:

  pre-commit:
    name: Pre-commit
    runs-on: Larger-Github-Runners
    steps:
      - uses: actions/checkout@v4

      - name: Check workflow files
        uses: docker://rhysd/actionlint:latest
        env:
          SHELLCHECK_OPTS: --exclude=SC2086,SC2129,SC2046
        with:
          args: -color

      - uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install -y libudev-dev

      - name: Install Aptos CLI
        run: |
          curl -fsSL "https://aptos.dev/scripts/install_cli.py" | python3
          aptos --version
          aptos update movefmt
          echo 'Adding movefmt to PATH'
          echo "$HOME/.aptos/bin" >> $GITHUB_PATH

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 9
          run_install: true

      - name: Install Python
        uses: actions/setup-python@v5

      - name: Run Pre Commit
        uses: pre-commit/action@v3.0.1

  fmt:
    name: fmt
    runs-on: Larger-Github-Runners
    env:
      RUST_BACKTRACE: 1
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install -y libudev-dev

      - name: Install Aptos CLI
        run: |
          curl -fsSL "https://aptos.dev/scripts/install_cli.py" | python3
          aptos --version
          aptos update movefmt
          echo 'Adding movefmt to PATH'
          echo "$HOME/.aptos/bin" >> $GITHUB_PATH

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 9
          run_install: true

      - name: Fmt Project
        run: make fmt

  codespell:
    runs-on: Larger-Github-Runners
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: codespell-project/actions-codespell@v2
        with:
          skip: "*.json"

  lint:
    name: lint
    needs:
      - codespell
    runs-on: Larger-Github-Runners
    steps:
      - uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install -y libudev-dev

      - name: Install Aptos CLI
        run: |
          curl -fsSL "https://aptos.dev/scripts/install_cli.py" | python3
          aptos --version
          aptos update movefmt
          echo 'Adding movefmt to PATH'
          echo "$HOME/.aptos/bin" >> $GITHUB_PATH

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 9
          run_install: true

      - name: Install Python
        uses: actions/setup-python@v5

      - name: Lint project
        run: make lint
