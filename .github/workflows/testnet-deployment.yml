name: Testnet Deployment

on:
  workflow_dispatch:
    inputs:
      network:
        description: 'Network'
        required: true
        default: 'testnet'
        type: choice
        options:
          - testnet
          - devnet
      initData:
        description: 'Initialize Data'
        required: true
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'
      upgradeContracts:
        description: 'Upgrade Contracts'
        required: true
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'

jobs:
  testnet-deployment:
    name: Deploy protocol on Testnet
    runs-on: Larger-Github-Runners
    timeout-minutes: 60
    env:
      # common settings
      CARGO_TERM_COLOR: always
      GITHUB_ACTIONS: true
      MOVE_VERSION: 2.1
      COMPILER_VERSION: 2.0
      ARTIFACTS_LEVEL: all
      DEFAULT_FUND_AMOUNT: 40000000
      APTOS_NETWORK: ${{ github.event.inputs.network }}
      UPGRADE_CONTRACTS: ${{ github.event.inputs.upgradeContracts }}
      DEFAULT_FUNDER_PRIVATE_KEY: ${{ secrets.GH_DEFAULT_FUNDER_PRIVATE_KEY }}

      # external profiles
      CHAINLINK_DATA_FEEDS: ${{ secrets.GH_CHAINLINK_DATA_FEEDS }}
      CHAINLINK_PLATFORM: ${{ secrets.GH_CHAINLINK_PLATFORM }}

      # aave profiles
      AAVE_ACL_PRIVATE_KEY: ${{ secrets.GH_AAVE_ACL_PRIVATE_KEY }}
      AAVE_CONFIG_PRIVATE_KEY: ${{ secrets.GH_AAVE_CONFIG_PRIVATE_KEY }}
      AAVE_MATH_PRIVATE_KEY: ${{ secrets.GH_AAVE_MATH_PRIVATE_KEY }}
      AAVE_ORACLE_PRIVATE_KEY: ${{ secrets.GH_AAVE_ORACLE_PRIVATE_KEY }}
      AAVE_POOL_PRIVATE_KEY: ${{ secrets.GH_AAVE_POOL_PRIVATE_KEY }}
      A_TOKENS_PRIVATE_KEY: ${{ secrets.GH_A_TOKENS_PRIVATE_KEY }}
      AAVE_MOCK_UNDERLYING_TOKENS_PRIVATE_KEY: ${{ secrets.GH_AAVE_MOCK_UNDERLYING_TOKENS_PRIVATE_KEY }}
      VARIABLE_TOKENS_PRIVATE_KEY: ${{ secrets.GH_VARIABLE_TOKENS_PRIVATE_KEY }}
      AAVE_LARGE_PACKAGES_PRIVATE_KEY: ${{ secrets.GH_AAVE_LARGE_PACKAGES_PRIVATE_KEY }}
      AAVE_DATA_PRIVATE_KEY: ${{ secrets.GH_AAVE_DATA_PRIVATE_KEY }}

      # test profiles
      TEST_ACCOUNT_0_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_0_PRIVATE_KEY }}
      TEST_ACCOUNT_1_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_1_PRIVATE_KEY }}
      TEST_ACCOUNT_2_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_2_PRIVATE_KEY }}
      TEST_ACCOUNT_3_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_3_PRIVATE_KEY }}
      TEST_ACCOUNT_4_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_4_PRIVATE_KEY }}
      TEST_ACCOUNT_5_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_5_PRIVATE_KEY }}

    steps:
      - uses: actions/checkout@v4

      - uses: dtolnay/rust-toolchain@stable

      - name: Install dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install -y libudev-dev lld libdw-dev

      - name: Install Aptos CLI
        uses: ./.github/actions/install-aptos-cli
        with:
          version: "6.2.0"

      - name: Set Aptos Workspace Config
        run: make set-workspace-config

      - name: Init Aptos Workspace Config
        run: make init-workspace-config

      - name: Run Aptos Create Profiles
        run: make init-profiles

      - name: Run Aptos Create Test Profiles
        run: make init-test-profiles

      - name: Run Aptos Top-Up Profiles
        run: make top-up-profiles

      - name: Run Aptos Top-Up Test Profiles
        run: make top-up-test-profiles

      - name: Print Aptos Config File
        run: cat ./.aptos/config.yaml

      - name: Run Aptos Compile All Contracts
        run: make compile-all

      - name: Run Aptos Publish All Contracts
        run: make publish-all

      - name: Run Script - Configure Acl
        run: make configure-acl

      - name: Run Script - Create Reserves
        run: make create-reserves

      - name: Run Script - Create Emodes
        run: make create-emodes

      - name: Run Script - Configure Reserves
        run: make configure-reserves

      - name: Run Script - Configure Interest Rates
        run: make configure-interest-rates

      - name: Run Script - Configure Interest Rates
        run: make configure-interest-rates

      - name: Run Script - Configure Price Feeds
        run: make configure-price-feeds
