name: TypeScript Integration Tests

on:
  pull_request:
  merge_group:
    branches:
      - main

env:
  CARGO_TERM_COLOR: always
  GITHUB_ACTIONS: true
  APTOS_NETWORK: local
  MOVE_VERSION: 2.1
  COMPILER_VERSION: 2.0
  UPGRADE_CONTRACTS: true
  ARTIFACTS_LEVEL: all
  DEFAULT_FUND_AMOUNT: 100000000
  DEFAULT_FUNDER_PRIVATE_KEY: ${{ secrets.GH_DEFAULT_FUNDER_PRIVATE_KEY }}

  # aave profiles
  AAVE_ACL_PRIVATE_KEY: ${{ secrets.GH_AAVE_ACL_PRIVATE_KEY }}
  AAVE_CONFIG_PRIVATE_KEY: ${{ secrets.GH_AAVE_CONFIG_PRIVATE_KEY }}
  AAVE_MATH_PRIVATE_KEY: ${{ secrets.GH_AAVE_MATH_PRIVATE_KEY }}
  AAVE_ORACLE_PRIVATE_KEY: ${{ secrets.GH_AAVE_ORACLE_PRIVATE_KEY }}
  AAVE_POOL_PRIVATE_KEY: ${{ secrets.GH_AAVE_POOL_PRIVATE_KEY }}
  A_TOKENS_PRIVATE_KEY: ${{ secrets.GH_A_TOKENS_PRIVATE_KEY }}
  AAVE_MOCK_UNDERLYING_TOKENS_PRIVATE_KEY: ${{ secrets.GH_AAVE_MOCK_UNDERLYING_TOKENS_PRIVATE_KEY }}
  VARIABLE_TOKENS_PRIVATE_KEY: ${{ secrets.GH_VARIABLE_TOKENS_PRIVATE_KEY }}
  AAVE_LARGE_PACKAGES_PRIVATE_KEY: ${{ secrets.GH_AAVE_LARGE_PACKAGES_PRIVATE_KEY }}
  AAVE_DATA_PRIVATE_KEY: ${{ secrets.GH_AAVE_DATA_PRIVATE_KEY }}
  AAVE_DATA_FEEDS_PRIVATE_KEY: ${{ secrets.GH_AAVE_DATA_FEEDS_PRIVATE_KEY }}
  AAVE_PLATFORM_PRIVATE_KEY: ${{ secrets.GH_AAVE_PLATFORM_PRIVATE_KEY }}

  # test profiles
  TEST_ACCOUNT_0_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_0_PRIVATE_KEY }}
  TEST_ACCOUNT_1_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_1_PRIVATE_KEY }}
  TEST_ACCOUNT_2_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_2_PRIVATE_KEY }}
  TEST_ACCOUNT_3_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_3_PRIVATE_KEY }}
  TEST_ACCOUNT_4_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_4_PRIVATE_KEY }}
  TEST_ACCOUNT_5_PRIVATE_KEY: ${{ secrets.GH_TEST_ACCOUNT_5_PRIVATE_KEY }}

jobs:

  typescript-integration-tests:
    name: Run Typescript Integration Tests
    runs-on: Larger-Github-Runners
    timeout-minutes: 60
    env:
      DEFAULT_FUNDER_PRIVATE_KEY: ${{ secrets.GH_DEFAULT_FUNDER_PRIVATE_KEY }}
    steps:
      - uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install -y libudev-dev lld libdw-dev

      - name: Install Aptos CLI
        uses: ./.github/actions/install-aptos-cli
        with:
          version: "6.2.0"

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 9
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install npm dependencies
        run: pnpm install
        working-directory: test-suites

      - name: Run Aptos Create Local Testnet
        run: |
          make local-testnet &
          echo $! > aptos_serve_pid.txt

      - name: Wait for Aptos Local Testnet to be ready
        run: |
          sleep 20

      - name: Set Local Aptos Workspace Config
        run: make set-workspace-config

      - name: Init Local Aptos Workspace Config
        run: make init-workspace-config

      - name: Run Aptos Create Profiles
        run: make init-profiles

      - name: Run Aptos Create Test Profiles
        run: make init-test-profiles

      - name: Run Aptos Fund Profiles
        run: make fund-profiles

      - name: Run Aptos Fund Test Profiles
        run: make fund-test-profiles

      - name: Print Aptos Config File
        run: cat ./.aptos/config.yaml

      - name: Run Aptos Publish All Contracts
        run: make publish-all

      - name: Run init test data
        run: pnpm deploy:init-data
        working-directory: test-suites

      - name: Run integration tests logic
        run: pnpm test:all
        working-directory: test-suites

      - name: Terminate Aptos Local Testnet
        if: always()
        run: |-
          kill $(cat aptos_serve_pid.txt) || true
