name: Unit Tests & Coverage

on:
  pull_request:
  merge_group:
    branches:
      - main

env:
  CARGO_TERM_COLOR: always
  GITHUB_ACTIONS: true
  APTOS_NETWORK: local
  MOVE_VERSION: 2.1
  COMPILER_VERSION: 2.0
  UPGRADE_CONTRACTS: true
  ARTIFACTS_LEVEL: all
  DEFAULT_FUND_AMOUNT: 40000000
  DEFAULT_FUNDER_PRIVATE_KEY: ${{ secrets.GH_DEFAULT_FUNDER_PRIVATE_KEY }}

  # aave profiles
  AAVE_ACL_PRIVATE_KEY: ${{ secrets.GH_AAVE_ACL_PRIVATE_KEY }}
  AAVE_CONFIG_PRIVATE_KEY: ${{ secrets.GH_AAVE_CONFIG_PRIVATE_KEY }}
  AAVE_MATH_PRIVATE_KEY: ${{ secrets.GH_AAVE_MATH_PRIVATE_KEY }}
  AAVE_ORACLE_PRIVATE_KEY: ${{ secrets.GH_AAVE_ORACLE_PRIVATE_KEY }}
  AAVE_POOL_PRIVATE_KEY: ${{ secrets.GH_AAVE_POOL_PRIVATE_KEY }}
  A_TOKENS_PRIVATE_KEY: ${{ secrets.GH_A_TOKENS_PRIVATE_KEY }}
  AAVE_MOCK_UNDERLYING_TOKENS_PRIVATE_KEY: ${{ secrets.GH_AAVE_MOCK_UNDERLYING_TOKENS_PRIVATE_KEY }}
  VARIABLE_TOKENS_PRIVATE_KEY: ${{ secrets.GH_VARIABLE_TOKENS_PRIVATE_KEY }}
  AAVE_LARGE_PACKAGES_PRIVATE_KEY: ${{ secrets.GH_AAVE_LARGE_PACKAGES_PRIVATE_KEY }}
  AAVE_DATA_PRIVATE_KEY: ${{ secrets.GH_AAVE_DATA_PRIVATE_KEY }}
  AAVE_DATA_FEEDS_PRIVATE_KEY: ${{ secrets.GH_AAVE_DATA_FEEDS_PRIVATE_KEY }}
  AAVE_PLATFORM_PRIVATE_KEY: ${{ secrets.GH_AAVE_PLATFORM_PRIVATE_KEY }}

jobs:
  unit-tests:
    name: Run Aptos Unit Tests
    runs-on: Larger-Github-Runners
    timeout-minutes: 60
    env:
      DEFAULT_FUNDER_PRIVATE_KEY: ${{ secrets.GH_DEFAULT_FUNDER_PRIVATE_KEY }}
    steps:
      - uses: actions/checkout@v4

      - name: Install Aptos CLI
        uses: ./.github/actions/install-aptos-cli
        with:
          version: "6.2.0"

      - name: Run Aptos Create Local Testnet
        run: |
          make local-testnet &
          echo $! > aptos_serve_pid.txt

      - name: Wait for Aptos Local Testnet to be ready
        run: sleep 20

      - name: Set Local Aptos Workspace Config
        run: make set-workspace-config

      - name: Init Local Aptos Workspace Config
        run: make init-workspace-config

      - name: Run Aptos Create Profiles
        run: make init-profiles

      - name: Run Aptos Fund Profiles
        run: make fund-profiles

      - name: Run Aptos Compile All Contracts
        run: make compile-all

      - name: Run Aptos Test All Contracts
        run: make test-all

      - name: Run Coverage for All Contracts
        run: make coverage-all

      - name: Compute Move Coverage
        run: |
          chmod +x coverage/coverage.py
          python3 coverage/coverage.py

      - name: Upload to Codecov.io
        uses: codecov/codecov-action@v5
        with:
          name: move-coverage
          flags: move
          fail_ci_if_error: true
          verbose: true
          files: coverage.lcov
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Terminate Aptos Local Testnet
        if: always()
        run: |-
          kill $(cat aptos_serve_pid.txt) || true
