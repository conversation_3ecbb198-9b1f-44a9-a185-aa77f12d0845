name: npm audit

on:
  pull_request:
  merge_group:
    branches:
      - main
  schedule:
    - cron: '0 10 * * *'

jobs:
  npm-scan:
    name: aave-test-kit
    runs-on: Larger-Github-Runners
    steps:
      - uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        working-directory: aave-test-kit

      - name: Run pnpm audit
        run: pnpm audit
        working-directory: aave-test-kit
