name: 🐛 Bug Report
description: Something is wrong within the aptos implementation.

labels:
  - bug
  - triage

body:
  - type: markdown
    attributes:
      value: |
        Thank you for helping to improve our protocol!

        ## The best bug report is a failing test!
        The fastest way to get a bug fixed is to add a failing test and send a pull request. **It's usually easy to do with our bug report test template**.
        1. Click the "Fork" button
        2. Open our contributions guideline!
        3. Follow the instructions!

        ## I'd rather just report the bug
        That's fine too! Go ahead and fill out this form.
  - type: input
    attributes:
      label: What version of the protocol are you seeing this issue with?
    validations:
      required: true
  - type: textarea
    attributes:
      label: What is happening?
      description: A concise description of what you're experiencing.
    validations:
      required: true
  - type: textarea
    attributes:
      label: steps to reproduce
      description: Steps to reproduce the behavior.
    validations:
      required: true
  - type: textarea
    attributes:
      label: How it should behave?
      description: A concise description of what you expected to happen.
    validations:
      required: true
  - type: dropdown
    attributes:
      label: What os are you seeing the problem on?
      description: Android, iOS, Windows, Mac
      options:
        - Android Mobile
        - iOS Mobile
        - Windows Desktop
        - Mac/Apple Desktop
        - Linux
