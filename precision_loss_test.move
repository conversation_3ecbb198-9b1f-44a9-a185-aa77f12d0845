/// Test to demonstrate precision loss in debt calculations
module test::precision_loss_test {
    use aave_math::wad_ray_math;

    #[test]
    fun test_precision_loss_in_ray_div() {
        // RAY = 10^27
        let ray = wad_ray_math::ray();
        
        // Test case 1: Small amount with high index
        let small_amount = 1; // 1 wei
        let high_index = ray + ray / 2; // 1.5 RAY (50% interest accrued)
        
        // Calculate scaled amount: amount / index
        let scaled_amount = wad_ray_math::ray_div(small_amount, high_index);
        
        // This should be 0 due to precision loss
        assert!(scaled_amount == 0, 1);
        
        // Test case 2: Slightly larger amount
        let larger_amount = high_index / 2; // This should give scaled_amount = 0.5, which rounds to 1
        let scaled_amount_2 = wad_ray_math::ray_div(larger_amount, high_index);
        
        // This should be 1 due to rounding
        assert!(scaled_amount_2 == 1, 2);
        
        // Test case 3: Demonstrate the "free debt" scenario
        // If we have scaled_amount = 0, then balance_of will return 0
        let calculated_debt = wad_ray_math::ray_mul(scaled_amount, high_index);
        assert!(calculated_debt == 0, 3); // No debt recorded despite borrowing
        
        // But if we have scaled_amount = 1, we get more debt than borrowed
        let calculated_debt_2 = wad_ray_math::ray_mul(scaled_amount_2, high_index);
        assert!(calculated_debt_2 > larger_amount, 4); // More debt than borrowed
    }
    
    #[test]
    fun test_cumulative_precision_loss() {
        let ray = wad_ray_math::ray();
        let index = ray + ray / 10; // 1.1 RAY (10% interest)
        
        // Simulate multiple small borrows
        let total_actual_borrowed = 0;
        let total_scaled_debt = 0;
        
        // Borrow 10 times, each time 1 wei
        let i = 0;
        while (i < 10) {
            let borrow_amount = 1;
            let scaled_amount = wad_ray_math::ray_div(borrow_amount, index);
            
            total_actual_borrowed = total_actual_borrowed + borrow_amount;
            total_scaled_debt = total_scaled_debt + scaled_amount;
            
            i = i + 1;
        };
        
        // Calculate what the protocol thinks the debt is
        let calculated_total_debt = wad_ray_math::ray_mul(total_scaled_debt, index);
        
        // The protocol underreports the debt
        assert!(calculated_total_debt < total_actual_borrowed, 5);
        
        // In this case, we borrowed 10 wei but the protocol thinks we owe less
        assert!(total_actual_borrowed == 10, 6);
        assert!(calculated_total_debt < 10, 7);
    }
    
    #[test]
    fun test_minimum_borrow_threshold() {
        let ray = wad_ray_math::ray();
        let index = ray; // 1.0 RAY (no interest yet)
        
        // Find the minimum amount that results in non-zero scaled debt
        let min_amount = 1;
        let scaled = wad_ray_math::ray_div(min_amount, index);
        
        // With index = RAY, ray_div(1, RAY) should be 0 due to precision loss
        assert!(scaled == 0, 8);
        
        // The minimum amount that gives scaled debt = 1 is approximately RAY/2 + 1
        let threshold_amount = ray / 2 + 1;
        let scaled_threshold = wad_ray_math::ray_div(threshold_amount, index);
        assert!(scaled_threshold == 1, 9);
        
        // This means any borrow less than ~5*10^26 wei will be "free"
        // For an 18-decimal token, this is about 0.5 tokens
    }
}
