#!/usr/bin/env python3
"""
Mathematical proof of the isolation mode debt ceiling bypass vulnerability
in Aave's Move implementation.
"""

def demonstrate_vulnerability():
    """
    Demonstrates the precision loss vulnerability in isolation mode debt tracking.
    """
    print("=== Aave Isolation Mode Debt Ceiling Bypass Vulnerability ===\n")
    
    # Constants from the codebase
    DEBT_CEILING_DECIMALS = 2  # From reserve_config.move:79
    TOKEN_DECIMALS = 18        # USDC-like token
    
    # Calculate scaling factor
    decimals = TOKEN_DECIMALS - DEBT_CEILING_DECIMALS  # 18 - 2 = 16
    scaling_factor = 10 ** decimals  # 10^16
    
    print(f"Token decimals: {TOKEN_DECIMALS}")
    print(f"Debt ceiling decimals: {DEBT_CEILING_DECIMALS}")
    print(f"Scaling decimals: {decimals}")
    print(f"Scaling factor: {scaling_factor:,}")
    print()
    
    # Demonstrate the vulnerability
    print("=== Vulnerability Demonstration ===")
    
    # Vulnerable borrow amount (just below scaling factor)
    vulnerable_amount = 9 * (10 ** 15)  # 0.009 USDC in wei
    
    print(f"Vulnerable borrow amount: {vulnerable_amount:,} wei")
    print(f"Vulnerable borrow amount: {vulnerable_amount / (10**18):.6f} USDC")
    print()
    
    # Calculate debt units (this is what gets added to isolation mode tracking)
    debt_units = vulnerable_amount // scaling_factor  # Integer division
    
    print(f"Debt units calculation: {vulnerable_amount:,} / {scaling_factor:,} = {debt_units}")
    print(f"Result: {debt_units} units added to isolation mode debt tracking")
    print()
    
    # Demonstrate multiple borrows
    num_borrows = 100
    total_borrowed = vulnerable_amount * num_borrows
    total_debt_units = debt_units * num_borrows
    
    print(f"=== Exploitation Scenario ===")
    print(f"Number of small borrows: {num_borrows}")
    print(f"Total amount borrowed: {total_borrowed:,} wei")
    print(f"Total amount borrowed: {total_borrowed / (10**18):.3f} USDC")
    print(f"Total debt units tracked: {total_debt_units}")
    print()
    
    # Show the bypass
    debt_ceiling = 100000  # 1000 USDC with 2 decimals (100000 units)
    print(f"Debt ceiling: {debt_ceiling:,} units ({debt_ceiling/100:.0f} USDC)")
    print(f"Tracked debt after exploit: {total_debt_units} units")
    print(f"Debt ceiling breached: {'No' if total_debt_units <= debt_ceiling else 'Yes'}")
    print(f"Actual debt created: {total_borrowed / (10**18):.3f} USDC")
    print()
    
    # Show what happens with larger amounts
    print("=== Comparison with Larger Amounts ===")
    larger_amount = 1 * (10 ** 16)  # 0.01 USDC (exactly at scaling factor)
    larger_debt_units = larger_amount // scaling_factor
    
    print(f"Larger borrow amount: {larger_amount:,} wei (0.01 USDC)")
    print(f"Debt units: {larger_amount:,} / {scaling_factor:,} = {larger_debt_units}")
    print(f"This would be properly tracked!")
    print()
    
    # Calculate minimum exploitable amount
    print("=== Minimum Exploitable Amount ===")
    max_untracked = scaling_factor - 1
    print(f"Maximum untracked amount: {max_untracked:,} wei")
    print(f"Maximum untracked amount: {max_untracked / (10**18):.6f} USDC")
    print(f"Any amount below {scaling_factor:,} wei will truncate to 0 debt units")

if __name__ == "__main__":
    demonstrate_vulnerability()
