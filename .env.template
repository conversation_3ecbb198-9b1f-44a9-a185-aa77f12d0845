# put your aptos config file path here e.g. /home/<USER>/.aptos/config.yaml or leave uncommented if default workspace...
APTOS_NETWORK=local
UPGRADE_CONTRACTS=false
ARTIFACTS_LEVEL=all
MOVE_VERSION=2.2
COMPILER_VERSION=2.0
DEFAULT_FUND_AMOUNT=********
CHAINLINK_DATA_FEEDS ?= 0x100
CHAINLINK_PLATFORM ?= 0x200

# aave profiles private keys
AAVE_ACL_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_CONFIG_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_MATH_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_ORACLE_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_POOL_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
A_TOKENS_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_MOCK_UNDERLYING_TOKENS_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
VARIABLE_TOKENS_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_LARGE_PACKAGES_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_DATA_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_DATA_FEEDS_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
AAVE_PLATFORM_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
DEFAULT_FUNDER_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]

# test profiles private keys
TEST_ACCOUNT_0_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
TEST_ACCOUNT_1_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
TEST_ACCOUNT_2_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
TEST_ACCOUNT_3_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
TEST_ACCOUNT_4_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]
TEST_ACCOUNT_5_PRIVATE_KEY=[YOUR PROFILE ACCOUNT PRIVATE KEY]

# acl admins
POOL_ADMINS=[ADDRESS]
ASSET_LISTING_ADMINS=[ADDRESS]
RISK_ADMINS=[ADDRESS]
FUND_ADMINS=[ADDRESS]
EMERGENCY_ADMINS=[ADDRESS]
FLASH_BORROWER_ADMINS=[ADDRESS]
EMISSION_ADMINS=[ADDRESS]
ECOSYSTEM_ADMINS=[ADDRESS]
REWARDS_ADMINS=[ADDRESS]
