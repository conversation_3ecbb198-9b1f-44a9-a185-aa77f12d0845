services:
  hasura:
    image: hasura/graphql-engine:v2.44.0-ce
    container_name: hasura
    hostname: hasura
    ports:
      - 8092:8080
    restart: always
    depends_on:
      - postgres
    environment:
      PG_DATABASE_URL: ******************************************/indexer
      HASURA_GRAPHQL_METADATA_DATABASE_URL: ******************************************/indexer
      INDEXER_V2_POSTGRES_URL: ******************************************/indexer
      HASURA_GRAPHQL_ENABLE_CONSOLE: true
      HASURA_GRAPHQL_CONSOLE_ASSETS_DIR: /srv/console-assets
    volumes:
      - console-assets:/srv/console-assets
    networks:
      - aptos-local-testnet-network

  postgres:
    image: postgres
    container_name: postgres
    hostname: postgres
    restart: always
    ports:
      - 5432:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: indexer
    # healthcheck:
    #   test: ["CMD", "pg_isready", "-U", "postgres"]
    #   interval: 10s
    #   timeout: 5s
    #   retries: 5
    volumes:
      - postgres:/var/lib/postgresql/data
      - ./postgres-init:/docker-entrypoint-initdb.d
    networks:
      - aptos-local-testnet-network

  aave-testnet:
    env_file:
      - ../.env
    build:
      context: ..
      dockerfile: aave-test-kit/Dockerfile
    container_name: aave-testnet
    restart: always
    ports:
      - 8070:8070
      - 8080:8080
      - 8081:8081
      - 8090:8090
      - 9101:9101
      - 50051:50051
    depends_on:
      - postgres
      - hasura
    environment:
      APTOS_NETWORK: local
      UPGRADE_CONTRACTS: true
      ARTIFACTS_LEVEL: all
      DEFAULT_FUND_AMOUNT: 100000000
      DEFAULT_FUNDER_PRIVATE_KEY: 0x0
      AAVE_ACL_PRIVATE_KEY: 0x0
      AAVE_CONFIG_PRIVATE_KEY: 0x0
      AAVE_MATH_PRIVATE_KEY: 0x0
      AAVE_ORACLE_PRIVATE_KEY: 0x0
      AAVE_POOL_PRIVATE_KEY: 0x0
      A_TOKENS_PRIVATE_KEY: 0x0
      AAVE_MOCK_UNDERLYING_TOKENS_PRIVATE_KEY: 0x0
      VARIABLE_TOKENS_PRIVATE_KEY: 0x0
      AAVE_LARGE_PACKAGES_PRIVATE_KEY: 0x0
    networks:
      - aptos-local-testnet-network

volumes:
  postgres: {}
  console-assets: {}

networks:
  aptos-local-testnet-network:
    name: aptos-local-testnet-network
    driver: bridge
