{"name": "aave-test-kit", "version": "1.0.0", "description": "Test kit for the aave aptos protocol", "main": "dist/index.js", "scripts": {"up": "pnpm update", "check": "pnpm outdated", "upgrade": "pnpm run up && pnpm run check", "audit": "pnpm audit --audit-level=high", "test": "jest", "test:cov": "jest --coverage", "check-ts": "tsc", "lint": "eslint . --no-cache", "lint:fix": "eslint . --fix --no-cache", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "fmt": "pnpm run prettier:fix && pnpm run lint:fix", "fmt:check": "pnpm run prettier && pnpm run lint", "generate": "tsx cli/index.ts generate ../.env", "build": "docker compose  build --no-cache", "start": "docker compose  up --build", "stop": "docker compose  down"}, "author": "AAVE", "license": "ISC", "dependencies": {"@aave/aave-v3-aptos-ts-sdk": "^0.0.38", "@aptos-labs/aptos-client": "^1.2.0", "@aptos-labs/ts-sdk": "1.38.0", "@ethersproject/bignumber": "^5.8.0", "bignumber.js": "^9.3.0", "commander": "^13.1.0", "dotenv": "^16.5.0", "ethers": "6.13.7", "jest-environment-jsdom": "^29.7.0", "ref-array-di": "^1.2.2", "toml": "^3.0.0", "tslib": "^2.8.1", "tsx": "^4.19.4", "util": "^0.12.5", "yaml": "^2.7.1"}, "devDependencies": {"@aptos-labs/aptos-cli": "^1.0.2", "@jest/globals": "^29.7.0", "@types/bn.js": "^5.1.6", "@types/chai": "5.2.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.3", "eslint": "^9.26.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.1.2", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^50.6.11", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-tsdoc": "^0.4.0", "jest": "^29.7.0", "node-ts": "^8.0.2", "prettier": "^3.5.3", "prettier-eslint": "^16.4.1", "prettier-standard": "^16.4.1", "rimraf": "^6.0.1", "tree-kill": "^1.2.2", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1"}, "pnpm": {"overrides": {"trim@<0.0.3": ">=0.0.3", "postcss@<7.0.36": ">=7.0.36", "postcss@<8.4.31": ">=8.4.31", "minimatch@<3.0.5": ">=3.0.5", "minimist@>=1.0.0 <1.2.6": ">=1.2.6", "semver@>=6.0.0 <6.3.1": ">=6.3.1", "axios@<1.8.2": ">=1.8.2"}}}