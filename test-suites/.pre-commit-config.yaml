default_install_hook_types:
  - pre-commit

default_stages:
  - pre-commit

exclude: |
  (?x)^(
    CHANGELOG.md|
    pnpm-lock.yaml|
    cov-reports
  )$

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-json
      - id: check-toml
      - id: check-added-large-files
        args:
          - --maxkb=3000
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: detect-private-key

  - repo: local
    hooks:
      - id: format
        name: format
        description: Format files using different tools
        entry: make fmt
        language: system
        pass_filenames: false

  - repo: https://github.com/lyz-code/yamlfix/
    rev: 1.17.0
    hooks:
      - id: yamlfix
        exclude: pnpm-lock.yaml
        args:
          - -c
          - .yamlfix.toml
