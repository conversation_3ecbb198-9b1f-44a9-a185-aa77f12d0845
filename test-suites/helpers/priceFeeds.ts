import { AAVE, DAI, LINK, USDC, WBTC, WETH } from "../configs/tokens";
import { hexToUint8Array } from "./common";

export const priceFeeds: Map<string, Uint8Array> = new Map<string, Uint8Array>();

// Simulated Chainlink price feeds
priceFeeds.set("APT", hexToUint8Array("0x011e22d6bf000332000000000000000000000000000000000000000000000000"));
priceFeeds.set(USDC, hexToUint8Array("0x01a80ff216000332000000000000000000000000000000000000000000000000"));
priceFeeds.set("USDT", hexToUint8Array("0x016d06ebb6000332000000000000000000000000000000000000000000000000"));
priceFeeds.set("BTC", hexToUint8Array("0x01a0b4d920000332000000000000000000000000000000000000000000000000"));
priceFeeds.set(WETH, hexToUint8Array("0x01d585327c000332000000000000000000000000000000000000000000000000"));
priceFeeds.set(LINK, hexToUint8Array("0x0101199b3b000332000000000000000000000000000000000000000000000000"));
priceFeeds.set(WBTC, hexToUint8Array("0x02d585327c000332000000000000000000000000000000000000000000000000"));
priceFeeds.set(AAVE, hexToUint8Array("0xb9d00f98aa4a8e3b6622b49b741bfd9b6aa6f22012bac3fc86f2188ba7d066f6"));
priceFeeds.set(DAI, hexToUint8Array("0x02d947528bda8d5ab6d0f304b950f06f9ff85e29fbffd725e4a56239843e3a68"));
