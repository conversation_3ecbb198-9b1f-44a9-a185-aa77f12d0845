{"name": "ts-test", "version": "1.0.0", "description": "Typescript test suite & scripts for the Aave V3 protocol", "main": "dist/index.js", "scripts": {"up": "pnpm update", "check": "pnpm outdated", "upgrade": "pnpm up && pnpm check", "audit": "pnpm audit --audit-level=high", "test": "jest", "test:cov": "jest --coverage", "deploy:init-data": "tsx scripts/initData.ts", "deploy:core-operations": "tsx scripts/coreOperations.ts", "check-ts": "tsc", "lint": "eslint . --no-cache", "lint:fix": "eslint . --fix --no-cache", "prettier": "prettier --check .", "prettier:fix": "prettier --write \"**/*.@(json|md|sh|toml)\"", "prettier:validate": "prettier --check \"**/*.@(json|md|sh|toml)\"", "fmt": "pnpm prettier:fix && pnpm lint:fix", "fmt:check": "pnpm prettier && pnpm lint", "md:lint": "markdownlint -c .markdownlint.json \"**/*.md\" \".github/**/*.md\" -i \"target\" -i \"node_modules\" -i \"CHANGELOG.md\" -i \"aave-core/doc/**/*.md\"", "md:fix": "pnpm md:lint --fix", "test:oracle": "jest test/aave-oracle.spec.ts", "test:acl-manager": "jest test/acl-manager.spec.ts", "test:rate-strategy": "jest test/rate-strategy.spec.ts", "test:pool-drop-reserve": "jest test/pool-drop-reserve.spec.ts", "test:pool-get-reserve-address-by-id": "jest test/pool-get-reserve-address-by-id.spec.ts", "test:wadraymath": "jest test/wadraymath.spec.ts", "test:pool-edge": "jest pool-edge.spec.ts", "test:config-edge": "jest configurator-edge.spec.ts", "test:config": "jest configurator.spec.ts", "test:rescue-tokens": "jest rescue-tokens.spec.ts", "test:supply": "jest supply.spec.ts", "test:withdraw": "jest withdraw.spec.ts", "test:borrow": "jest borrow.spec.ts ", "test:repay": "jest repay.spec.ts", "test:repay-atoken": "jest repay-atoken.spec.ts", "test:liquidation": "jest liquidation.spec.ts", "test:liquidation-underlying": "jest liquidation-underlying.spec.ts", "test:all": "run-s test:oracle test:acl-manager test:rate-strategy test:pool-drop-reserve test:pool-get-reserve-address-by-id test:wadraymath test:pool-edge test:config-edge test:config test:rescue-tokens test:supply test:withdraw test:borrow test:repay test:repay-atoken test:liquidation test:liquidation-underlying", "test:standalone": "jest test/aave-oracle.spec.ts test/acl-manager.spec.ts test/rate-strategy.spec.ts pool-drop-reserve.spec.ts pool-get-reserve-address-by-id.spec.ts wadraymath.spec.ts", "test:logic": "pnpm test:standalone && pnpm test:pool-edge && pnpm test:config-edge && pnpm test:config && pnpm test:rescue-tokens && pnpm test:supply && pnpm test:withdraw && pnpm test:borrow && pnpm test:repay && pnpm test:repay-atoken && pnpm test:liquidation && pnpm test:liquidation-underlying", "test:samples": "jest test/aave-oracle.spec.ts test/acl-manager.spec.ts test/atoken-delegation-aware.spec.ts test/atoken-event-accounting.spec.ts test/atoken-events.spec.ts test/atoken-modifiers.spec.ts test/atoken-permit.spec.ts test/configurator.spec.ts test/liquidation-atoken.spec.ts test/liquidation-edge.spec.ts test/liquidation-emode-interest.spec.ts test/liquidation-emode.spec.ts test/liquidation-with-fee.spec.ts test/liquidity-indexes.spec.ts test/ltv-validation.spec.ts test/mint-to-treasury.spec.ts test/pool-drop-reserve.spec.ts test/pool-edge.spec.ts test/pool-get-reserve-address-by-id.spec.ts test/rate-strategy.spec.ts test/supply.spec.ts test/variable-debt-token-events.spec.ts test/variable-debt-token.spec.ts test/wadraymath.spec.ts"}, "author": "AAVE", "license": "ISC", "dependencies": {"@aptos-labs/aptos-client": "^1.2.0", "@aptos-labs/ts-sdk": "1.38.0", "@ethersproject/bignumber": "^5.8.0", "bignumber.js": "^9.3.0", "chalk": "4.1.2", "ci": "^2.3.0", "dotenv": "^16.5.0", "ethers": "6.13.7", "jest-environment-jsdom": "^29.7.0", "ref-array-di": "^1.2.2", "run-all": "^1.0.1", "toml": "^3.0.0", "ts-node": "^10.9.2", "tslib": "^2.8.1", "tsx": "^4.19.4", "util": "^0.12.5", "yaml": "^2.7.1"}, "devDependencies": {"@aave/deploy-v3": "^1.56.2", "@aptos-labs/aptos-cli": "^1.0.2", "@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@jest/globals": "^29.7.0", "@types/bn.js": "^5.1.6", "@types/jest": "^29.5.14", "@types/node": "^22.15.3", "babel-jest": "^29.7.0", "eslint": "^9.26.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.1.2", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^50.6.11", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-tsdoc": "^0.4.0", "jest": "^29.7.0", "node-ts": "^8.0.2", "npm-run-all": "^4.1.5", "ora": "^8.2.0", "prettier": "^3.5.3", "prettier-eslint": "^16.4.1", "prettier-standard": "^16.4.1", "rimraf": "^6.0.1", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1", "webpack": "^5.99.7", "webpack-cli": "^6.0.1"}, "pnpm": {"overrides": {"trim@<0.0.3": ">=0.0.3", "postcss@<7.0.36": ">=7.0.36", "postcss@<8.4.31": ">=8.4.31", "minimatch@<3.0.5": ">=3.0.5", "minimist@>=1.0.0 <1.2.6": ">=1.2.6", "ws@>=7.0.0 <7.5.10": ">=7.5.10", "elliptic@>=4.0.0 <=6.5.6": ">=6.5.7", "elliptic@>=2.0.0 <=6.5.6": ">=6.5.7", "elliptic@>=5.2.1 <=6.5.6": ">=6.5.7", "cookie@<0.7.0": ">=0.7.0", "semver@>=6.0.0 <6.3.1": ">=6.3.1", "elliptic@<6.6.0": ">=6.6.0", "elliptic@<6.5.6": ">=6.5.6", "elliptic@<=6.6.0": ">=6.6.1", "base-x@<=3.0.10": ">=3.0.11", "undici@<5.29.0": ">=5.29.0"}}}