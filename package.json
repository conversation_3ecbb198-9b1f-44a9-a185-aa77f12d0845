{"name": "aave-v3", "version": "0.0.0", "license": "MIT", "private": true, "scripts": {"prettier:fix": "prettier --write \"**/*.@(json|md|sh|toml)\"", "prettier:validate": "prettier --check \"**/*.@(json|md|sh|toml)\"", "md:lint": "markdownlint -c .markdownlint.json \"**/*.md\" \".github/**/*.md\" -i \"target\" -i \"node_modules\" -i \"CHANGELOG.md\" -i \"aave-core/doc/**/*.md\"", "md:fix": "pnpm md:lint --fix"}, "devDependencies": {"@commitlint/config-conventional": "^19.8.0", "commitlint": "^19.8.0", "markdownlint": "^0.38.0", "markdownlint-cli": "^0.44.0", "prettier": "^3.5.3", "prettier-plugin-sh": "^0.17.2", "prettier-plugin-toml": "^2.0.4"}}